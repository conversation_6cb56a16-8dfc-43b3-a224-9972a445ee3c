@echo off
REM ================================================================================
REM Java Environment Setup Script (Windows Version)
REM Purpose: Set correct Java 8 environment variables
REM ================================================================================

setlocal enabledelayedexpansion

REM Java 8 environment path (Windows format)
REM Note: Please modify the following path according to actual installation path
set "DEFAULT_JAVA8_HOME=%USERPROFILE%\.jdks\corretto-1.8.0_452"

REM If default path doesn't exist, try common Java installation paths
if not exist "%DEFAULT_JAVA8_HOME%" (
    REM Try Program Files paths
    if exist "%ProgramFiles%\Amazon Corretto\jdk1.8.0_452" (
        set "DEFAULT_JAVA8_HOME=%ProgramFiles%\Amazon Corretto\jdk1.8.0_452"
    ) else if exist "%ProgramFiles(x86)%\Amazon Corretto\jdk1.8.0_452" (
        set "DEFAULT_JAVA8_HOME=%ProgramFiles(x86)%\Amazon Corretto\jdk1.8.0_452"
    ) else if exist "%ProgramFiles%\Java\jdk1.8.0_452" (
        set "DEFAULT_JAVA8_HOME=%ProgramFiles%\Java\jdk1.8.0_452"
    ) else if exist "%ProgramFiles(x86)%\Java\jdk1.8.0_452" (
        set "DEFAULT_JAVA8_HOME=%ProgramFiles(x86)%\Java\jdk1.8.0_452"
    ) else if exist "C:\Users\<USER>\AppData\Local\Programs\Eclipse Adoptium\jdk-8.0.452.9-hotspot" (
        set "DEFAULT_JAVA8_HOME=C:\Users\<USER>\AppData\Local\Programs\Eclipse Adoptium\jdk-8.0.452.9-hotspot"
    )
)

REM Set Java environment variables
set "JAVA_HOME=%DEFAULT_JAVA8_HOME%"
set "PATH=%JAVA_HOME%\bin;%PATH%"

REM Maven environment variables
set "MAVEN_OPTS=-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
set "MAVEN_OPTS=%MAVEN_OPTS% -Djava.security.policy=all.policy"
REM Use quotes to handle paths with spaces
set "MAVEN_OPTS=%MAVEN_OPTS% -Djava.home=\"%JAVA_HOME%\""

REM Verify Java installation
if not exist "%JAVA_HOME%\bin\java.exe" (
    echo [ERROR] Java JDK not found: %JAVA_HOME%
    echo Please check Java installation path or modify DEFAULT_JAVA8_HOME variable in script
    exit /b 1
)

echo Java environment variables have been set:
echo   JAVA_HOME: %JAVA_HOME%

REM Get Java version information
"%JAVA_HOME%\bin\java.exe" -version 2>&1 | findstr "version" > temp_version.txt
set /p JAVA_VERSION_LINE=<temp_version.txt
del temp_version.txt
echo   Java version: %JAVA_VERSION_LINE%

echo.
echo Maven options: %MAVEN_OPTS%
echo.
echo Environment variable setup completed!

REM Export environment variables to caller environment
endlocal & (
    set "JAVA_HOME=%JAVA_HOME%"
    set "PATH=%PATH%"
    set "MAVEN_OPTS=%MAVEN_OPTS%"
)
