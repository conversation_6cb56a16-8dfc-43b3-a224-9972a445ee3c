package com.sdesrd.filetransfer.server.service;

import java.io.File;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.dto.FileInfo;
import com.sdesrd.filetransfer.server.dto.FileMetadata;
import com.sdesrd.filetransfer.server.entity.FileTransferRecord;
import com.sdesrd.filetransfer.server.exception.FileTransferException;
import com.sdesrd.filetransfer.server.mapper.FileTransferRecordMapper;
import com.sdesrd.filetransfer.server.util.FileUtils;
import com.sdesrd.filetransfer.server.util.UlidUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据库故障回退服务类
 * 
 * <p>提供SQLite数据库故障时的回退机制，通过扫描磁盘上的info.json元数据文件
 * 来提供文件上传、下载和查询功能。确保系统在数据库不可用时仍能正常运行。</p>
 * 
 * <p>主要功能包括：</p>
 * <ul>
 *   <li>数据库健康状态检测</li>
 *   <li>基于info.json的文件查找和信息获取</li>
 *   <li>磁盘扫描和文件重建</li>
 *   <li>回退模式下的文件服务</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 2024-01-15
 */
@Slf4j
@Service
public class DatabaseFallbackService {
    
    /**
     * 数据库健康检查超时时间（毫秒）
     */
    private static final long DATABASE_HEALTH_CHECK_TIMEOUT_MS = 5000L;
    
    /**
     * 数据库健康检查间隔时间（毫秒）
     */
    private static final long DATABASE_HEALTH_CHECK_INTERVAL_MS = 30000L;
    
    /**
     * 最大扫描文件数量限制（防止内存溢出）
     */
    private static final int MAX_SCAN_FILES_LIMIT = 10000;
    
    /**
     * 数据库健康状态缓存
     */
    private final AtomicBoolean databaseHealthy = new AtomicBoolean(true);
    
    /**
     * 最后一次健康检查时间
     */
    private final AtomicLong lastHealthCheckTime = new AtomicLong(0);
    
    @Autowired
    private FileTransferRecordMapper transferRecordMapper;
    
    @Autowired
    private FileTransferProperties properties;
    
    @Autowired
    private FileMetadataService metadataService;
    
    /**
     * 检查数据库健康状态
     * 
     * @return 如果数据库可用则返回true，否则返回false
     */
    public boolean isDatabaseHealthy() {
        long currentTime = System.currentTimeMillis();
        long lastCheck = lastHealthCheckTime.get();
        
        // 如果距离上次检查时间未超过间隔，返回缓存的结果
        if (currentTime - lastCheck < DATABASE_HEALTH_CHECK_INTERVAL_MS) {
            return databaseHealthy.get();
        }
        
        // 执行健康检查
        boolean healthy = performDatabaseHealthCheck();
        databaseHealthy.set(healthy);
        lastHealthCheckTime.set(currentTime);
        
        return healthy;
    }
    
    /**
     * 强制刷新数据库健康状态
     * 
     * @return 当前数据库健康状态
     */
    public boolean refreshDatabaseHealth() {
        boolean healthy = performDatabaseHealthCheck();
        databaseHealthy.set(healthy);
        lastHealthCheckTime.set(System.currentTimeMillis());
        
        log.info("强制刷新数据库健康状态: {}", healthy ? "健康" : "故障");
        return healthy;
    }
    
    /**
     * 基于fileId查找文件信息（回退模式）
     * 
     * @param fileId 文件标识符
     * @param username 用户名
     * @return 文件信息，如果不存在则返回null
     */
    public FileInfo findFileByIdFallback(String fileId, String username) {
        if (!StringUtils.hasText(fileId)) {
            return null;
        }
        
        try {
            String storagePath = properties.getDefaultConfig().getStoragePath();
            
            // 尝试读取info.json元数据文件
            FileMetadata metadata = metadataService.readMetadata(fileId, storagePath);
            if (metadata != null && metadata.isComplete()) {
                return convertMetadataToFileInfo(metadata);
            }
            
            // 如果没有元数据文件，尝试基于路径规则查找物理文件
            String physicalFilePath = tryFindPhysicalFile(fileId, storagePath);
            if (physicalFilePath != null) {
                return buildFileInfoFromPhysicalFile(fileId, physicalFilePath);
            }
            
            log.debug("回退模式：未找到文件 - fileId: {}, 用户: {}", fileId, username);
            return null;
            
        } catch (Exception e) {
            log.error("回退模式查找文件失败 - fileId: {}, 用户: {}", fileId, username, e);
            return null;
        }
    }
    
    /**
     * 基于相对路径查找文件信息（回退模式）
     * 
     * @param relativePath 相对路径
     * @param username 用户名
     * @return 文件信息，如果不存在则返回null
     */
    public FileInfo findFileByPathFallback(String relativePath, String username) {
        if (!StringUtils.hasText(relativePath)) {
            return null;
        }
        
        try {
            String storagePath = properties.getDefaultConfig().getStoragePath();
            String absolutePath = Paths.get(storagePath, relativePath).toString();
            
            // 检查文件是否存在
            File physicalFile = new File(absolutePath);
            if (!physicalFile.exists() || !physicalFile.isFile()) {
                log.debug("回退模式：物理文件不存在 - 路径: {}", absolutePath);
                return null;
            }
            
            // 尝试从路径中提取fileId
            String fileId = extractFileIdFromPath(relativePath);
            if (fileId != null) {
                // 尝试读取对应的元数据文件
                FileMetadata metadata = metadataService.readMetadata(fileId, storagePath);
                if (metadata != null && metadata.isComplete()) {
                    return convertMetadataToFileInfo(metadata);
                }
            }
            
            // 如果没有元数据文件，基于物理文件构建基本信息
            return buildFileInfoFromPhysicalFile(fileId, absolutePath);
            
        } catch (Exception e) {
            log.error("回退模式通过路径查找文件失败 - 路径: {}, 用户: {}", relativePath, username, e);
            return null;
        }
    }
    
    /**
     * 扫描存储目录并重建数据库（回退模式辅助功能）
     * 
     * @return 重建结果统计信息
     */
    public Map<String, Object> scanAndRebuildFromMetadata() {
        Map<String, Object> result = new HashMap<>();
        String storagePath = properties.getDefaultConfig().getStoragePath();
        
        int scannedFiles = 0;
        int rebuiltRecords = 0;
        int skippedFiles = 0;
        List<String> errors = new ArrayList<>();
        
        try {
            File baseDir = new File(storagePath);
            if (!baseDir.exists() || !baseDir.isDirectory()) {
                throw new FileTransferException("存储目录不存在: " + storagePath);
            }
            
            log.info("开始从元数据文件扫描重建数据库 - 存储路径: {}", storagePath);
            
            // 遍历年月目录（YYYYMM格式）
            try (DirectoryStream<Path> yearMonthDirs = Files.newDirectoryStream(baseDir.toPath())) {
                for (Path yearMonthDir : yearMonthDirs) {
                    if (!Files.isDirectory(yearMonthDir)) {
                        continue;
                    }
                    
                    // 遍历fileId目录
                    try (DirectoryStream<Path> fileIdDirs = Files.newDirectoryStream(yearMonthDir)) {
                        for (Path fileIdDir : fileIdDirs) {
                            if (!Files.isDirectory(fileIdDir)) {
                                continue;
                            }
                            
                            if (scannedFiles >= MAX_SCAN_FILES_LIMIT) {
                                log.warn("达到最大扫描文件数量限制: {}", MAX_SCAN_FILES_LIMIT);
                                break;
                            }
                            
                            scannedFiles++;
                            String fileId = fileIdDir.getFileName().toString();
                            
                            try {
                                // 读取info.json元数据文件
                                FileMetadata metadata = metadataService.readMetadata(fileId, storagePath);
                                if (metadata != null && metadata.isComplete()) {
                                    // 检查数据库中是否已存在该记录
                                    if (isDatabaseHealthy() && !fileRecordExists(fileId)) {
                                        // 将元数据转换为数据库记录并插入
                                        FileTransferRecord record = convertMetadataToRecord(metadata);
                                        transferRecordMapper.insert(record);
                                        rebuiltRecords++;
                                        
                                        log.debug("重建数据库记录 - fileId: {}, 原文件名: {}", 
                                                 fileId, metadata.getOriginalFileName());
                                    } else {
                                        skippedFiles++;
                                    }
                                } else {
                                    skippedFiles++;
                                    log.debug("跳过无效元数据文件 - fileId: {}", fileId);
                                }
                                
                            } catch (Exception e) {
                                skippedFiles++;
                                String error = "处理文件失败 - fileId: " + fileId + ", 错误: " + e.getMessage();
                                errors.add(error);
                                log.warn(error, e);
                            }
                        }
                    }
                }
            }
            
            result.put("success", true);
            result.put("message", "扫描重建完成");
            
        } catch (Exception e) {
            log.error("扫描重建数据库失败", e);
            result.put("success", false);
            result.put("message", "扫描重建失败: " + e.getMessage());
        }
        
        result.put("scannedFiles", scannedFiles);
        result.put("rebuiltRecords", rebuiltRecords);
        result.put("skippedFiles", skippedFiles);
        result.put("errors", errors);
        result.put("storagePath", storagePath);
        
        log.info("扫描重建完成 - 扫描: {}, 重建: {}, 跳过: {}, 错误: {}", 
                scannedFiles, rebuiltRecords, skippedFiles, errors.size());
        
        return result;
    }
    
    /**
     * 执行数据库健康检查
     * 
     * @return 数据库是否健康
     */
    private boolean performDatabaseHealthCheck() {
        try {
            long startTime = System.currentTimeMillis();
            
            // 尝试执行简单的数据库查询
            QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
            query.last("LIMIT 1");
            transferRecordMapper.selectList(query);
            
            long duration = System.currentTimeMillis() - startTime;
            
            if (duration > DATABASE_HEALTH_CHECK_TIMEOUT_MS) {
                log.warn("数据库响应缓慢 - 耗时: {}ms, 阈值: {}ms", duration, DATABASE_HEALTH_CHECK_TIMEOUT_MS);
                return false;
            }
            
            log.debug("数据库健康检查通过 - 耗时: {}ms", duration);
            return true;
            
        } catch (DataAccessException e) {
            log.warn("数据库健康检查失败 - 数据访问异常: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("数据库健康检查异常", e);
            return false;
        }
    }
    
    /**
     * 尝试基于新的ULID存储规则查找物理文件
     * 
     * @param fileId 文件标识符
     * @param storagePath 存储根路径
     * @return 物理文件路径，如果不存在则返回null
     */
    private String tryFindPhysicalFile(String fileId, String storagePath) {
        try {
            // 如果fileId是ULID格式，从中提取年月信息
            if (UlidUtils.isValidUlid(fileId)) {
                String yearMonth = UlidUtils.extractYearMonth(fileId);
                if (yearMonth != null) {
                    Path fileIdDir = Paths.get(storagePath, yearMonth, fileId);
                    if (Files.exists(fileIdDir) && Files.isDirectory(fileIdDir)) {
                        // 查找目录下的第一个普通文件（排除info.json）
                        try (DirectoryStream<Path> files = Files.newDirectoryStream(fileIdDir)) {
                            for (Path file : files) {
                                if (Files.isRegularFile(file) && !file.getFileName().toString().equals("info.json")) {
                                    return file.toString();
                                }
                            }
                        }
                    }
                }
            }
            
            return null;
            
        } catch (Exception e) {
            log.debug("查找物理文件失败 - fileId: {}", fileId, e);
            return null;
        }
    }
    
    /**
     * 从相对路径中提取fileId
     * 
     * @param relativePath 相对路径
     * @return fileId，如果无法提取则返回null
     */
    private String extractFileIdFromPath(String relativePath) {
        try {
            Path path = Paths.get(relativePath);
            // 相对路径格式：YYYYMM/fileId/fileName
            if (path.getNameCount() >= 2) {
                return path.getName(1).toString();
            }
            return null;
        } catch (Exception e) {
            log.debug("从路径提取fileId失败 - 路径: {}", relativePath, e);
            return null;
        }
    }
    
    /**
     * 基于物理文件构建文件信息
     * 
     * @param fileId 文件ID
     * @param filePath 物理文件路径
     * @return 文件信息
     */
    private FileInfo buildFileInfoFromPhysicalFile(String fileId, String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                return null;
            }
            
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileId(fileId != null ? fileId : "unknown");
            fileInfo.setFileName(file.getName());
            fileInfo.setFileSize(file.length());
            fileInfo.setFormattedSize(FileUtils.formatFileSize(file.length()));
            fileInfo.setLastModified(file.lastModified());
            fileInfo.setCanRead(file.canRead());
            fileInfo.setCanWrite(file.canWrite());
            
            // 计算相对路径
            String storagePath = properties.getDefaultConfig().getStoragePath();
            if (filePath.startsWith(storagePath)) {
                String relativePath = filePath.substring(storagePath.length() + 1);
                fileInfo.setRelativePath(relativePath.replace('\\', '/'));
            }
            
            // 检测文件类型
            String extension = getFileExtension(file.getName());
            fileInfo.setExtension(extension);
            fileInfo.setFileType(detectFileType(file.getName()));
            
            log.debug("基于物理文件构建文件信息 - fileId: {}, 文件名: {}, 大小: {}", 
                     fileId, file.getName(), file.length());
            
            return fileInfo;
            
        } catch (Exception e) {
            log.error("基于物理文件构建文件信息失败 - fileId: {}, 路径: {}", fileId, filePath, e);
            return null;
        }
    }
    
    /**
     * 将元数据转换为文件信息
     * 
     * @param metadata 文件元数据
     * @return 文件信息
     */
    private FileInfo convertMetadataToFileInfo(FileMetadata metadata) {
        FileInfo fileInfo = new FileInfo();
        
        fileInfo.setFileId(metadata.getFileId());
        fileInfo.setFileName(metadata.getOriginalFileName() != null ? 
                            metadata.getOriginalFileName() : metadata.getPhysicalFileName());
        fileInfo.setFileSize(metadata.getFileSize());
        fileInfo.setFormattedSize(metadata.getFormattedSize());
        fileInfo.setFileType(metadata.getFileType());
        fileInfo.setUploadTime(metadata.getUploadTime());
        fileInfo.setRelativePath(metadata.getRelativePath());
        
        if (metadata.getLastModified() != null) {
            try {
                // 简化的时间解析
                fileInfo.setLastModified(System.currentTimeMillis());
            } catch (Exception e) {
                fileInfo.setLastModified(System.currentTimeMillis());
            }
        }
        
        fileInfo.setCanRead(true);
        fileInfo.setCanWrite(false); // 上传完成的文件默认为只读
        fileInfo.setExtension(metadata.getFileExtension());
        
        return fileInfo;
    }
    
    /**
     * 检查文件记录是否存在
     * 
     * @param fileId 文件ID
     * @return 如果存在则返回true
     */
    private boolean fileRecordExists(String fileId) {
        try {
            QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
            query.eq("file_id", fileId);
            return transferRecordMapper.selectCount(query) > 0;
        } catch (Exception e) {
            log.debug("检查文件记录存在性失败 - fileId: {}", fileId, e);
            return false;
        }
    }
    
    /**
     * 将元数据转换为数据库记录
     * 
     * @param metadata 文件元数据
     * @return 文件传输记录
     */
    private FileTransferRecord convertMetadataToRecord(FileMetadata metadata) {
        FileTransferRecord record = new FileTransferRecord();
        
        record.setId(metadata.getTransferId());
        record.setFileId(metadata.getFileId());
        record.setFileName(metadata.getPhysicalFileName());
        record.setOriginalFileName(metadata.getOriginalFileName());
        record.setFileSize(metadata.getFileSize());
        record.setFileType(metadata.getFileType());
        record.setStatus(metadata.getStatus());
        record.setClientIp(metadata.getClientIp());
        record.setCreateTime(metadata.getCreateTime());
        record.setUpdateTime(metadata.getLastModified());
        record.setCompleteTime(metadata.getUploadTime());
        record.setTotalChunks(metadata.getTotalChunks());
        record.setCompletedChunks(metadata.getCompletedChunks());
        record.setTransferredSize(metadata.getFileSize());
        record.setExtInfo(metadata.getExtInfo());
        
        // 构建文件路径
        String storagePath = properties.getDefaultConfig().getStoragePath();
        if (metadata.getRelativePath() != null) {
            record.setFilePath(Paths.get(storagePath, metadata.getRelativePath()).toString());
        }
        
        return record;
    }
    
    /**
     * 获取文件扩展名
     * 
     * @param fileName 文件名
     * @return 扩展名（不包含点号）
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }
    
    /**
     * 检测文件类型
     * 
     * @param fileName 文件名
     * @return MIME类型
     */
    private String detectFileType(String fileName) {
        String extension = getFileExtension(fileName);
        
        // 简化的MIME类型映射
        switch (extension) {
            case "txt": return "text/plain";
            case "pdf": return "application/pdf";
            case "jpg": case "jpeg": return "image/jpeg";
            case "png": return "image/png";
            case "gif": return "image/gif";
            case "mp4": return "video/mp4";
            case "mp3": return "audio/mpeg";
            case "zip": return "application/zip";
            case "doc": return "application/msword";
            case "docx": return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls": return "application/vnd.ms-excel";
            case "xlsx": return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            default: return "application/octet-stream";
        }
    }
} 