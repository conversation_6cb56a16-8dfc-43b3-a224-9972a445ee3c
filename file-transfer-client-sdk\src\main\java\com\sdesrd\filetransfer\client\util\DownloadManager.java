package com.sdesrd.filetransfer.client.util;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;

import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.dto.DownloadResult;
import com.sdesrd.filetransfer.client.dto.TransferProgress;
import com.sdesrd.filetransfer.client.exception.FileTransferException;
import com.sdesrd.filetransfer.client.listener.TransferListener;

import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 增强的下载管理器
 * 支持多线程分片下载和智能断点续传
 */
@Slf4j
public class DownloadManager {
    
    /** 默认分片大小（2MB） */
    private static final long DEFAULT_CHUNK_SIZE = 2 * 1024 * 1024L;
    
    /** 最小分片大小（256KB） */
    private static final long MIN_CHUNK_SIZE = 256 * 1024L;
    
    /** 最大并发下载线程数 */
    private static final int MAX_CONCURRENT_DOWNLOADS = 8;
    
    /** 进度更新间隔（毫秒） */
    private static final long PROGRESS_UPDATE_INTERVAL_MS = 500L;
    
    private final ClientConfig config;
    private final OkHttpClient httpClient;
    private final ExecutorService downloadExecutor;
    
    public DownloadManager(ClientConfig config, OkHttpClient httpClient) {
        this.config = config;
        this.httpClient = httpClient;
        this.downloadExecutor = Executors.newFixedThreadPool(
                Math.min(config.getMaxConcurrentTransfers(), MAX_CONCURRENT_DOWNLOADS),
                r -> {
                    Thread t = new Thread(r, "DownloadManager-" + System.currentTimeMillis());
                    t.setDaemon(true);
                    return t;
                });
    }
    
    /**
     * 多线程分片下载文件
     * 
     * @param fileId 文件ID
     * @param savePath 保存路径
     * @param listener 传输监听器
     * @return 下载结果
     */
    public CompletableFuture<DownloadResult> downloadFileChunked(String fileId, String savePath, TransferListener listener) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return downloadFileChunkedSync(fileId, savePath, listener);
            } catch (Exception e) {
                log.error("分片下载失败 - 文件ID: {}, 保存路径: {}", fileId, savePath, e);
                throw new RuntimeException(e);
            }
        }, downloadExecutor);
    }
    
    /**
     * 同步分片下载文件
     */
    public DownloadResult downloadFileChunkedSync(String fileId, String savePath, TransferListener listener) throws FileTransferException {
        log.info("开始分片下载 - 文件ID: {}, 保存路径: {}", fileId, savePath);
        
        try {
            // 获取文件信息
            long fileSize = getFileSize(fileId);
            if (fileSize <= 0) {
                throw new FileTransferException("无法获取文件大小: " + fileId);
            }
            
            // 检查是否需要分片下载
            long chunkSize = calculateOptimalChunkSize(fileSize);
            if (fileSize <= chunkSize) {
                // 小文件直接下载
                return downloadFileDirect(fileId, savePath, listener);
            }
            
            // 创建目标文件
            File targetFile = new File(savePath);
            createTargetFile(targetFile, fileSize);
            
            // 计算分片信息
            List<ChunkInfo> chunks = calculateChunks(fileSize, chunkSize);
            log.info("分片下载计划 - 文件大小: {}, 分片数: {}, 分片大小: {}", 
                    FileUtils.formatFileSize(fileSize), chunks.size(), FileUtils.formatFileSize(chunkSize));
            
            // 初始化进度跟踪
            AtomicLong downloadedBytes = new AtomicLong(0);
            TransferProgress progress = createInitialProgress(fileId, fileSize, chunks.size());
            
            // 通知开始下载
            if (listener != null) {
                listener.onStart(progress);
            }
            
            // 启动进度更新任务
            CompletableFuture<Void> progressTask = startProgressUpdater(progress, downloadedBytes, fileSize, listener);
            
            try {
                // 并发下载所有分片
                List<CompletableFuture<Void>> chunkFutures = new ArrayList<>();
                for (ChunkInfo chunk : chunks) {
                    CompletableFuture<Void> chunkFuture = downloadChunk(fileId, targetFile, chunk, downloadedBytes);
                    chunkFutures.add(chunkFuture);
                }
                
                // 等待所有分片下载完成
                CompletableFuture.allOf(chunkFutures.toArray(new CompletableFuture[0])).get();
                
                // 验证文件完整性
                if (downloadedBytes.get() != fileSize) {
                    throw new FileTransferException(String.format("文件下载不完整: 期望 %d 字节，实际 %d 字节", 
                            fileSize, downloadedBytes.get()));
                }
                
                // 完成进度更新
                progress.setTransferredSize(fileSize);
                progress.setProgress(100.0);
                progress.setCompleted(true);
                
                if (listener != null) {
                    listener.onCompleted(progress);
                }
                
                log.info("分片下载完成 - 文件ID: {}, 保存路径: {}, 文件大小: {}", 
                        fileId, savePath, FileUtils.formatFileSize(fileSize));
                
                // 更新下载统计信息
                ConcurrentTransferManager.addDownloadedBytes(fileSize);
                
                return DownloadResult.success(savePath, fileSize);
                
            } finally {
                // 停止进度更新任务
                progressTask.cancel(true);
            }
            
        } catch (Exception e) {
            log.error("分片下载失败 - 文件ID: {}, 保存路径: {}", fileId, savePath, e);
            
            if (listener != null) {
                TransferProgress errorProgress = new TransferProgress();
                errorProgress.setFileName("下载文件-" + fileId);
                listener.onError(errorProgress, e);
            }
            
            throw new FileTransferException("分片下载失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取文件大小
     */
    private long getFileSize(String fileId) throws IOException {
        String url = config.getServerUrl() + "/filetransfer/api/file/download/info/" + fileId;
        Request request = new Request.Builder()
                .url(url)
                .head() // 使用HEAD请求获取文件信息
                .addHeader("User-Agent", "FileTransferClient/1.0.0")
                .build();
        
        // 添加认证头
        request = AuthUtils.addAuthHeaders(request.newBuilder(), config.getUser(), config.getSecretKey()).build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("获取文件信息失败: " + response.code());
            }
            
            String contentLength = response.header("Content-Length");
            if (contentLength != null) {
                return Long.parseLong(contentLength);
            }
            
            throw new IOException("无法获取文件大小");
        }
    }
    
    /**
     * 计算最优分片大小
     */
    private long calculateOptimalChunkSize(long fileSize) {
        long chunkSize = config.getChunkSize();
        
        // 确保分片大小不小于最小值
        chunkSize = Math.max(chunkSize, MIN_CHUNK_SIZE);
        
        // 对于小文件，使用较小的分片
        if (fileSize < 10 * 1024 * 1024) { // 小于10MB
            chunkSize = Math.min(chunkSize, fileSize / 2);
        }
        
        // 确保至少有一个分片
        return Math.max(chunkSize, MIN_CHUNK_SIZE);
    }
    
    /**
     * 计算分片信息
     */
    private List<ChunkInfo> calculateChunks(long fileSize, long chunkSize) {
        List<ChunkInfo> chunks = new ArrayList<>();
        long offset = 0;
        int index = 0;
        
        while (offset < fileSize) {
            long currentChunkSize = Math.min(chunkSize, fileSize - offset);
            chunks.add(new ChunkInfo(index++, offset, currentChunkSize));
            offset += currentChunkSize;
        }
        
        return chunks;
    }
    
    /**
     * 创建目标文件
     */
    private void createTargetFile(File targetFile, long fileSize) throws IOException {
        // 创建父目录
        File parentDir = targetFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            if (!parentDir.mkdirs()) {
                throw new IOException("无法创建目录: " + parentDir.getAbsolutePath());
            }
        }
        
        // 创建空文件并设置大小
        try (RandomAccessFile raf = new RandomAccessFile(targetFile, "rw")) {
            raf.setLength(fileSize);
        }
    }
    
    /**
     * 创建初始进度对象
     */
    private TransferProgress createInitialProgress(String fileId, long fileSize, int totalChunks) {
        TransferProgress progress = new TransferProgress();
        progress.setFileName("下载文件-" + fileId);
        progress.setTotalSize(fileSize);
        progress.setTransferredSize(0L);
        progress.setProgress(0.0);
        progress.setTotalChunks(totalChunks);
        progress.setCompletedChunks(0);
        progress.setCompleted(false);
        return progress;
    }
    
    /**
     * 启动进度更新任务
     */
    private CompletableFuture<Void> startProgressUpdater(TransferProgress progress, AtomicLong downloadedBytes, 
                                                        long totalSize, TransferListener listener) {
        if (listener == null) {
            return CompletableFuture.completedFuture(null);
        }
        
        return CompletableFuture.runAsync(() -> {
            long lastUpdateTime = System.currentTimeMillis();
            long lastDownloadedBytes = 0;
            
            while (!progress.isCompleted() && !Thread.currentThread().isInterrupted()) {
                try {
                    Thread.sleep(PROGRESS_UPDATE_INTERVAL_MS);
                    
                    long currentTime = System.currentTimeMillis();
                    long currentDownloadedBytes = downloadedBytes.get();
                    
                    // 更新进度
                    progress.setTransferredSize(currentDownloadedBytes);
                    progress.setProgress((double) currentDownloadedBytes / totalSize * 100);
                    
                    // 计算速度
                    long timeDiff = currentTime - lastUpdateTime;
                    if (timeDiff > 0) {
                        long bytesDiff = currentDownloadedBytes - lastDownloadedBytes;
                        long speed = bytesDiff * 1000 / timeDiff; // bytes/second
                        progress.setSpeed(speed);
                        
                        // 计算剩余时间
                        if (speed > 0) {
                            long remainingBytes = totalSize - currentDownloadedBytes;
                            progress.setRemainingTime(remainingBytes / speed);
                        }
                    }
                    
                    listener.onProgress(progress);
                    
                    lastUpdateTime = currentTime;
                    lastDownloadedBytes = currentDownloadedBytes;
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }, downloadExecutor);
    }
    
    /**
     * 分片信息
     */
    private static class ChunkInfo {
        private final int index;
        private final long offset;
        private final long size;
        
        public ChunkInfo(int index, long offset, long size) {
            this.index = index;
            this.offset = offset;
            this.size = size;
        }
        
        public int getIndex() { return index; }
        public long getOffset() { return offset; }
        public long getSize() { return size; }
        public long getEndOffset() { return offset + size - 1; }
    }
    
    /**
     * 下载单个分片
     */
    private CompletableFuture<Void> downloadChunk(String fileId, File targetFile, ChunkInfo chunk, AtomicLong downloadedBytes) {
        return CompletableFuture.runAsync(() -> {
            try {
                RetryManager.executeWithRetry(() -> {
                    try {
                        downloadChunkWithRetry(fileId, targetFile, chunk, downloadedBytes);
                        return null;
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }, RetryManager.networkRetry().build(), "下载分片-" + chunk.getIndex());

            } catch (Exception e) {
                log.error("下载分片失败 - 文件ID: {}, 分片: {}, 偏移: {}, 大小: {}",
                        fileId, chunk.getIndex(), chunk.getOffset(), chunk.getSize(), e);
                throw new RuntimeException("下载分片失败: " + e.getMessage(), e);
            }
        }, downloadExecutor);
    }

    /**
     * 下载分片（带重试）
     */
    private void downloadChunkWithRetry(String fileId, File targetFile, ChunkInfo chunk, AtomicLong downloadedBytes) throws IOException {
        String url = config.getServerUrl() + "/filetransfer/api/file/download/chunk/" + fileId;

        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .addHeader("User-Agent", "FileTransferClient/1.0.0")
                .addHeader("Range", "bytes=" + chunk.getOffset() + "-" + chunk.getEndOffset());

        // 添加认证头
        Request request = AuthUtils.addAuthHeaders(requestBuilder, config.getUser(), config.getSecretKey()).build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("下载分片失败: HTTP " + response.code());
            }

            if (response.body() == null) {
                throw new IOException("响应体为空");
            }

            // 写入文件
            try (RandomAccessFile raf = new RandomAccessFile(targetFile, "rw")) {
                raf.seek(chunk.getOffset());

                byte[] buffer = new byte[8192];
                long totalRead = 0;
                int bytesRead;

                while ((bytesRead = response.body().byteStream().read(buffer)) != -1 && totalRead < chunk.getSize()) {
                    int bytesToWrite = (int) Math.min(bytesRead, chunk.getSize() - totalRead);
                    raf.write(buffer, 0, bytesToWrite);
                    totalRead += bytesToWrite;
                    downloadedBytes.addAndGet(bytesToWrite);
                }

                if (totalRead != chunk.getSize()) {
                    throw new IOException(String.format("分片下载不完整: 期望 %d 字节，实际 %d 字节",
                            chunk.getSize(), totalRead));
                }
            }

            log.debug("分片下载完成 - 文件ID: {}, 分片: {}, 大小: {}",
                    fileId, chunk.getIndex(), FileUtils.formatFileSize(chunk.getSize()));
        }
    }

    /**
     * 直接下载文件（小文件）
     */
    private DownloadResult downloadFileDirect(String fileId, String savePath, TransferListener listener) throws FileTransferException {
        log.debug("直接下载文件 - 文件ID: {}, 保存路径: {}", fileId, savePath);

        try {
            String url = config.getServerUrl() + "/filetransfer/api/file/download/" + fileId;
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "FileTransferClient/1.0.0");

            // 添加认证头
            Request request = AuthUtils.addAuthHeaders(requestBuilder, config.getUser(), config.getSecretKey()).build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new FileTransferException("下载文件失败: HTTP " + response.code());
                }

                if (response.body() == null) {
                    throw new FileTransferException("响应体为空");
                }

                // 获取文件大小
                long contentLength = response.body().contentLength();

                // 创建目标文件
                File targetFile = new File(savePath);
                File parentDir = targetFile.getParentFile();
                if (parentDir != null && !parentDir.exists()) {
                    if (!parentDir.mkdirs()) {
                        throw new FileTransferException("无法创建目录: " + parentDir.getAbsolutePath());
                    }
                }

                // 初始化进度
                TransferProgress progress = new TransferProgress();
                progress.setFileName("下载文件-" + fileId);
                progress.setTotalSize(contentLength);
                progress.setTransferredSize(0L);
                progress.setProgress(0.0);
                progress.setCompleted(false);

                if (listener != null) {
                    listener.onStart(progress);
                }

                // 下载文件
                long downloadedBytes = FileUtils.downloadToFile(response.body().byteStream(), targetFile, contentLength,
                        (transferred, total) -> {
                            if (listener != null) {
                                progress.setTransferredSize(transferred);
                                progress.setProgress((double) transferred / total * 100);
                                listener.onProgress(progress);
                            }
                        });

                // 完成下载
                progress.setTransferredSize(downloadedBytes);
                progress.setProgress(100.0);
                progress.setCompleted(true);

                if (listener != null) {
                    listener.onCompleted(progress);
                }

                log.info("直接下载完成 - 文件ID: {}, 保存路径: {}, 文件大小: {}",
                        fileId, savePath, FileUtils.formatFileSize(downloadedBytes));

                // 更新下载统计信息
                ConcurrentTransferManager.addDownloadedBytes(downloadedBytes);

                return DownloadResult.success(savePath, downloadedBytes);
            }

        } catch (Exception e) {
            log.error("直接下载失败 - 文件ID: {}, 保存路径: {}", fileId, savePath, e);

            if (listener != null) {
                TransferProgress errorProgress = new TransferProgress();
                errorProgress.setFileName("下载文件-" + fileId);
                listener.onError(errorProgress, e);
            }

            throw new FileTransferException("直接下载失败: " + e.getMessage(), e);
        }
    }

    /**
     * 关闭下载管理器
     */
    public void shutdown() {
        if (downloadExecutor != null && !downloadExecutor.isShutdown()) {
            downloadExecutor.shutdown();
        }
    }
}
