@echo off
REM Simple test build script to diagnose issues

setlocal enabledelayedexpansion

echo ========================================================
echo     Simple Build Test Script
echo     Time: %date% %time%
echo ========================================================

REM Test argument parsing
set "execution_mode=build-test"
set "custom_java_home="

echo [DEBUG] Initial execution_mode: %execution_mode%
echo [DEBUG] Arguments: %*

REM Parse arguments
:parse_args
if "%~1"=="" goto :args_done
echo [DEBUG] Processing argument: %~1
if "%~1"=="build" (
    set "execution_mode=build"
    echo [DEBUG] Set execution_mode to build
    shift
    goto :parse_args
)
if "%~1"=="build-test" (
    set "execution_mode=build-test"
    echo [DEBUG] Set execution_mode to build-test
    shift
    goto :parse_args
)
if "%~1"=="--java-home" (
    set "custom_java_home=%~2"
    echo [DEBUG] Set custom_java_home to %~2
    shift
    shift
    goto :parse_args
)
echo [ERROR] Unknown argument: %~1
exit /b 1

:args_done
echo [DEBUG] Final execution_mode: %execution_mode%
echo [DEBUG] Final custom_java_home: %custom_java_home%

REM Test Java environment setup
echo [INFO] Setting up Java environment...
call scripts\set-java-env-en.bat
if errorlevel 1 (
    echo [ERROR] Java environment setup failed
    exit /b 1
)

echo [INFO] Java environment setup completed

REM Test Maven check
echo [INFO] Checking Maven...
where mvn >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Maven not found
    exit /b 1
)

echo [INFO] Maven found

REM Test simple Maven command
echo [INFO] Testing Maven version...
mvn -version
if errorlevel 1 (
    echo [ERROR] Maven version check failed
    exit /b 1
)

echo [SUCCESS] All checks passed
echo [INFO] Execution mode was: %execution_mode%

if "%execution_mode%"=="build" (
    echo [INFO] Would execute build-only mode
) else (
    echo [INFO] Would execute build-test mode
)

echo [SUCCESS] Script completed successfully
