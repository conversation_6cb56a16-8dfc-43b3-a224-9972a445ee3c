#!/bin/bash

# ================================================================================
# Java环境诊断和JCE配置检查脚本
# ================================================================================

set -e  # 遇到错误立即退出

# ==================== 常量定义 ====================

# 脚本版本信息
readonly SCRIPT_VERSION="1.0.0"
readonly SCRIPT_NAME="Java环境诊断和JCE配置检查脚本"

# 默认Java 8 JDK路径
readonly DEFAULT_JAVA8_HOME="$HOME/.jdks/corretto-1.8.0_452"

# 颜色定义
readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[1;33m'
readonly COLOR_BLUE='\033[0;34m'
readonly COLOR_NC='\033[0m' # No Color

# ==================== 日志函数 ====================

# 信息日志
log_info() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_BLUE}[INFO]${COLOR_NC} ${timestamp} - ${message}"
}

# 成功日志
log_success() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_GREEN}[SUCCESS]${COLOR_NC} ${timestamp} - ${message}"
}

# 警告日志
log_warning() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_YELLOW}[WARNING]${COLOR_NC} ${timestamp} - ${message}"
}

# 错误日志
log_error() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_RED}[ERROR]${COLOR_NC} ${timestamp} - ${message}"
}

# ==================== 检查函数 ====================

# 显示脚本头部信息
show_header() {
    echo "========================================================"
    echo "    $SCRIPT_NAME"
    echo "    版本：$SCRIPT_VERSION"
    echo "    时间：$(date '+%Y-%m-%d %H:%M:%S')"
    echo "========================================================"
}

# 检查Java环境
check_java_environment() {
    log_info "检查Java环境..."
    
    # 检查JAVA_HOME环境变量
    if [ -n "$JAVA_HOME" ]; then
        log_info "JAVA_HOME环境变量已设置：$JAVA_HOME"
        if [ -x "$JAVA_HOME/bin/java" ]; then
            log_success "JAVA_HOME指向的Java可执行文件存在"
        else
            log_error "JAVA_HOME指向的Java可执行文件不存在或不可执行"
        fi
    else
        log_warning "JAVA_HOME环境变量未设置"
    fi
    
    # 检查默认Java 8路径
    if [ -d "$DEFAULT_JAVA8_HOME" ] && [ -x "$DEFAULT_JAVA8_HOME/bin/java" ]; then
        log_success "默认Java 8 JDK存在：$DEFAULT_JAVA8_HOME"
    else
        log_warning "默认Java 8 JDK不存在：$DEFAULT_JAVA8_HOME"
    fi
    
    # 检查系统Java
    if command -v java &> /dev/null; then
        local java_version=$(java -version 2>&1 | head -n 1)
        log_info "系统Java版本：$java_version"
        
        # 获取Java安装路径
        local java_home_detected=$(java -XshowSettings:properties -version 2>&1 | grep 'java.home' | awk '{print $3}')
        if [ -n "$java_home_detected" ]; then
            log_info "检测到的Java安装路径：$java_home_detected"
        fi
    else
        log_error "系统中未找到Java命令"
    fi
}

# 检查JCE配置
check_jce_configuration() {
    log_info "检查JCE（Java Cryptography Extension）配置..."
    
    # 使用当前Java环境检查JCE
    if command -v java &> /dev/null; then
        # 创建临时Java程序来检查JCE
        local temp_java_file="/tmp/JCECheck.java"
        local temp_class_file="/tmp/JCECheck.class"
        
        cat > "$temp_java_file" << 'EOF'
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.net.ssl.SSLContext;
import java.security.NoSuchAlgorithmException;

public class JCECheck {
    public static void main(String[] args) {
        System.out.println("=== JCE配置检查 ===");
        
        // 检查AES最大密钥长度
        try {
            int maxKeyLength = Cipher.getMaxAllowedKeyLength("AES");
            System.out.println("AES最大密钥长度: " + maxKeyLength + " bits");
            if (maxKeyLength >= 256) {
                System.out.println("✅ JCE无限制强度策略已启用");
            } else {
                System.out.println("❌ JCE无限制强度策略未启用（限制为" + maxKeyLength + "位）");
            }
        } catch (Exception e) {
            System.out.println("❌ 检查AES密钥长度时发生错误: " + e.getMessage());
        }
        
        // 检查SSL/TLS支持
        try {
            SSLContext sslContext = SSLContext.getInstance("TLS");
            System.out.println("✅ SSL/TLS上下文创建成功");
        } catch (Exception e) {
            System.out.println("❌ SSL/TLS上下文创建失败: " + e.getMessage());
        }
        
        // 检查常用加密算法
        String[] algorithms = {"AES", "DES", "RSA", "SHA-256", "MD5"};
        for (String algorithm : algorithms) {
            try {
                if (algorithm.equals("RSA")) {
                    Cipher.getInstance("RSA");
                } else if (algorithm.startsWith("SHA") || algorithm.equals("MD5")) {
                    java.security.MessageDigest.getInstance(algorithm);
                } else {
                    KeyGenerator.getInstance(algorithm);
                }
                System.out.println("✅ " + algorithm + " 算法支持正常");
            } catch (Exception e) {
                System.out.println("❌ " + algorithm + " 算法支持异常: " + e.getMessage());
            }
        }
        
        // 检查Java安全策略文件
        String javaHome = System.getProperty("java.home");
        System.out.println("Java安装目录: " + javaHome);
        
        String[] policyFiles = {
            javaHome + "/lib/security/java.policy",
            javaHome + "/lib/security/local_policy.jar",
            javaHome + "/lib/security/US_export_policy.jar"
        };
        
        for (String policyFile : policyFiles) {
            java.io.File file = new java.io.File(policyFile);
            if (file.exists()) {
                System.out.println("✅ 策略文件存在: " + policyFile);
            } else {
                System.out.println("⚠️  策略文件不存在: " + policyFile);
            }
        }
    }
}
EOF

        # 编译并运行JCE检查程序
        if javac "$temp_java_file" -d /tmp 2>/dev/null; then
            log_info "运行JCE配置检查程序..."
            java -cp /tmp JCECheck
            
            # 清理临时文件
            rm -f "$temp_java_file" "$temp_class_file"
        else
            log_error "编译JCE检查程序失败"
            rm -f "$temp_java_file"
        fi
    else
        log_error "无法运行JCE检查，Java命令不可用"
    fi
}

# 检查网络连接和SSL
check_network_ssl() {
    log_info "检查网络连接和SSL配置..."
    
    # 检查基本网络连接
    if ping -c 1 google.com &> /dev/null; then
        log_success "网络连接正常"
    else
        log_warning "网络连接可能存在问题"
    fi
    
    # 检查SSL连接
    if command -v openssl &> /dev/null; then
        log_info "测试SSL连接..."
        if echo | openssl s_client -connect google.com:443 -servername google.com &> /dev/null; then
            log_success "SSL连接测试成功"
        else
            log_warning "SSL连接测试失败"
        fi
    else
        log_warning "openssl命令不可用，跳过SSL连接测试"
    fi
}

# 提供JCE修复建议
provide_jce_fix_suggestions() {
    log_info "JCE配置修复建议..."
    
    echo ""
    echo "如果发现JCE配置问题，可以尝试以下解决方案："
    echo ""
    echo "1. 对于Java 8："
    echo "   - 下载并安装JCE无限制强度策略文件"
    echo "   - 从Oracle官网下载 jce_policy-8.zip"
    echo "   - 解压并复制 local_policy.jar 和 US_export_policy.jar 到 \$JAVA_HOME/jre/lib/security/"
    echo ""
    echo "2. 对于Java 9及以上版本："
    echo "   - JCE无限制强度策略默认启用"
    echo "   - 如有问题，检查 java.security 文件中的 crypto.policy 设置"
    echo ""
    echo "3. 环境变量设置："
    echo "   - 确保 JAVA_HOME 正确设置"
    echo "   - 确保 PATH 包含 \$JAVA_HOME/bin"
    echo ""
    echo "4. 如果使用Docker或容器："
    echo "   - 确保容器中的Java环境配置正确"
    echo "   - 检查容器的安全策略设置"
    echo ""
}

# ==================== 主程序 ====================

# 主函数
main() {
    show_header
    
    echo ""
    check_java_environment
    
    echo ""
    check_jce_configuration
    
    echo ""
    check_network_ssl
    
    echo ""
    provide_jce_fix_suggestions
    
    echo ""
    log_success "Java环境诊断完成"
}

# 显示帮助信息
show_help() {
    echo "========================================================"
    echo "    $SCRIPT_NAME"
    echo "    版本：$SCRIPT_VERSION"
    echo "========================================================"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项："
    echo "  --help                显示此帮助信息"
    echo ""
    echo "功能："
    echo "  - 检查Java环境配置"
    echo "  - 验证JCE（Java Cryptography Extension）配置"
    echo "  - 测试SSL/TLS连接"
    echo "  - 提供配置修复建议"
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 运行主程序
main
