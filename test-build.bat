@echo off
REM Simple test build script

echo Starting test build script...

REM Test 1: Check current directory
echo Current directory: %CD%

REM Test 2: Check if scripts directory exists
if exist "scripts" (
    echo Scripts directory exists
) else (
    echo Scripts directory NOT found
)

REM Test 3: Check if set-java-env-en.bat exists
if exist "scripts\set-java-env-en.bat" (
    echo Java environment script exists
) else (
    echo Java environment script NOT found
)

REM Test 4: Try to call the Java environment script
echo Calling Java environment script...
call scripts\set-java-env-en.bat
echo Java environment script completed with exit code: %errorlevel%

REM Test 5: Check Java
echo Checking Java...
where java >nul 2>&1
if errorlevel 1 (
    echo Java NOT found in PATH
) else (
    echo Java found in PATH
    java -version
)

REM Test 6: Check Maven
echo Checking Maven...
where mvn >nul 2>&1
if errorlevel 1 (
    echo Maven NOT found in PATH
) else (
    echo Maven found in PATH
    mvn -version
)

echo Test build script completed.
