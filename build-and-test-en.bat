@echo off
REM ================================================================================
REM File Transfer SDK Build and Test Script (Windows Version)
REM ================================================================================

setlocal enabledelayedexpansion

REM ==================== Constants ====================

set "SCRIPT_VERSION=3.0.0"
set "SCRIPT_NAME=File Transfer SDK Build and Test Script"

REM Project modules list
set "PROJECT_MODULES=file-transfer-server-sdk file-transfer-client-sdk file-transfer-client-demo"

REM Modules that need unit testing
set "TEST_MODULES=file-transfer-server-sdk file-transfer-client-sdk"

REM Demo modules (run demo programs instead of unit tests)
set "DEMO_MODULES=file-transfer-client-demo"

REM Standalone service module
set "STANDALONE_MODULE=file-transfer-server-standalone"

REM Timeout configuration constants (seconds)
set "BUILD_TIMEOUT_SECONDS=600"
set "TEST_TIMEOUT_SECONDS=1200"
set "SERVER_STARTUP_TIMEOUT_SECONDS=30"
set "SERVER_SHUTDOWN_TIMEOUT_SECONDS=15"
set "DEMO_TEST_TIMEOUT_SECONDS=300"

REM Port configuration
set "TEST_SERVER_PORT=49011"

REM Directory configuration
set "LOG_DIR=logs"
for /f "tokens=1-3 delims=/ " %%a in ('date /t') do set "DATE_STR=%%c%%a%%b"
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set "TIME_STR=%%a%%b"
set "MAIN_LOG=%LOG_DIR%\build-and-test-%DATE_STR%_%TIME_STR%.log"
set "SERVER_PID_FILE=%LOG_DIR%\test-server.pid"

REM Execution mode constants
set "MODE_BUILD=build"
set "MODE_BUILD_TEST=build-test"

REM ==================== Logging Functions ====================

:log_info
echo [INFO] %date% %time% - %~1
if exist "%MAIN_LOG%" echo [INFO] %date% %time% - %~1 >> "%MAIN_LOG%" 2>nul
goto :eof

:log_success
echo [SUCCESS] %date% %time% - %~1
if exist "%MAIN_LOG%" echo [SUCCESS] %date% %time% - %~1 >> "%MAIN_LOG%" 2>nul
goto :eof

:log_warning
echo [WARNING] %date% %time% - %~1
if exist "%MAIN_LOG%" echo [WARNING] %date% %time% - %~1 >> "%MAIN_LOG%" 2>nul
goto :eof

:log_error
echo [ERROR] %date% %time% - %~1
if exist "%MAIN_LOG%" echo [ERROR] %date% %time% - %~1 >> "%MAIN_LOG%" 2>nul
goto :eof

:log_step
echo [STEP %~1] %date% %time% - %~2
if exist "%MAIN_LOG%" echo [STEP %~1] %date% %time% - %~2 >> "%MAIN_LOG%" 2>nul
if exist "%MAIN_LOG%" echo ======================================== >> "%MAIN_LOG%" 2>nul
goto :eof

REM ==================== Utility Functions ====================

:init_logging
REM Create log directory
if not exist "%LOG_DIR%" (
    mkdir "%LOG_DIR%"
)

REM Create main log file
echo. > "%MAIN_LOG%"
call :log_info "Main log file: %MAIN_LOG%"
goto :eof

:show_header
echo ========================================================
echo     %SCRIPT_NAME%
echo     Version: %SCRIPT_VERSION%
echo     Time: %date% %time%
echo ========================================================
goto :eof

:check_command
set "command=%~1"
set "description=%~2"

where "%command%" >nul 2>&1
if errorlevel 1 (
    call :log_error "%description% not installed or not in PATH: %command%"
    exit /b 1
)
exit /b 0

REM ==================== Environment Check Functions ====================

:setup_java_environment
set "custom_java_home=%~1"

call :log_step "1" "Setting up Java environment"

REM If custom Java path is specified, temporarily modify environment variables
if not "%custom_java_home%"=="" (
    if exist "%custom_java_home%\bin\java.exe" (
        set "JAVA_HOME=%custom_java_home%"
        set "PATH=%custom_java_home%\bin;%PATH%"
        set "MAVEN_OPTS=-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
        set "MAVEN_OPTS=!MAVEN_OPTS! -Djava.security.policy=all.policy"
        set "MAVEN_OPTS=!MAVEN_OPTS! -Djava.home=%custom_java_home%"
        call :log_info "Using specified Java JDK: %custom_java_home%"
        goto :java_env_done
    ) else (
        call :log_error "Specified Java JDK path is invalid: %custom_java_home%"
        exit /b 1
    )
) else (
    REM Use set-java-env.bat script to set environment variables
    set "set_java_script=scripts\set-java-env-en.bat"
    if exist "!set_java_script!" (
        call :log_info "Calling Java environment setup script: !set_java_script!"
        call "!set_java_script!"
        if not errorlevel 1 (
            call :log_info "Java environment setup script executed successfully"
        ) else (
            call :log_warning "Java environment setup script failed, trying system default Java"
        )
    ) else (
        call :log_warning "Java environment setup script not found: !set_java_script!"
        call :log_warning "Using system default Java environment"
    )
)

:java_env_done
REM Verify Java command availability
call :check_command "java" "Java Runtime"
if errorlevel 1 exit /b 1

REM Get Java version information
for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr "version"') do (
    set "java_version=%%i"
    goto :java_version_done
)
:java_version_done
call :log_info "Current Java version: %java_version%"

REM Verify Java version compatibility
echo %java_version% | findstr "1.8." >nul
if not errorlevel 1 (
    call :log_success "Using Java 8, fully compatible"
) else (
    echo %java_version% | findstr /r "^\"1[1-9]\." >nul
    if not errorlevel 1 (
        call :log_warning "Using Java %java_version%, project configured for Java 8, but should be backward compatible"
    ) else (
        call :log_warning "Current Java version: %java_version%, may have compatibility issues"
    )
)

exit /b 0

:check_maven_environment
call :log_step "2" "Checking Maven environment"

REM Check Maven command
call :check_command "mvn" "Apache Maven"
if errorlevel 1 exit /b 1

REM Get Maven version information
for /f "tokens=*" %%i in ('mvn -version 2^>^&1 ^| findstr "Apache Maven"') do (
    set "maven_version=%%i"
    goto :maven_version_done
)
:maven_version_done
call :log_info "Maven version: %maven_version%"

REM If MAVEN_OPTS not set by set-java-env.bat, use default configuration
if "%MAVEN_OPTS%"=="" (
    call :log_info "MAVEN_OPTS not set, using default configuration"
    set "MAVEN_OPTS=-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
    
    if not "%JAVA_HOME%"=="" (
        set "MAVEN_OPTS=!MAVEN_OPTS! -Djava.home=%JAVA_HOME%"
        call :log_info "Maven configured to use Java: %JAVA_HOME%"
    )
    
    REM Check if javadoc command is available
    if not "%JAVA_HOME%"=="" (
        if not exist "%JAVA_HOME%\bin\javadoc.exe" (
            call :log_warning "javadoc command not available, will skip Javadoc generation"
            set "MAVEN_OPTS=!MAVEN_OPTS! -Dmaven.javadoc.skip=true"
        )
    ) else (
        call :log_warning "JAVA_HOME not set, will skip Javadoc generation"
        set "MAVEN_OPTS=!MAVEN_OPTS! -Dmaven.javadoc.skip=true"
    )
) else (
    call :log_info "Using already set MAVEN_OPTS"
)

call :log_info "Maven options: %MAVEN_OPTS%"
call :log_success "Maven environment check completed"

exit /b 0

:validate_project_structure
call :log_step "3" "Validating project structure"

REM Check root pom.xml
if not exist "pom.xml" (
    call :log_error "Root pom.xml file does not exist"
    exit /b 1
)
call :log_info "Root pom.xml file exists"

REM Check each module directory
set "missing_modules="
for %%m in (%PROJECT_MODULES%) do (
    if not exist "%%m" (
        set "missing_modules=!missing_modules! %%m"
    ) else (
        call :log_info "Module directory exists: %%m"
        
        REM Check module pom.xml
        if not exist "%%m\pom.xml" (
            call :log_warning "Module pom.xml does not exist: %%m\pom.xml"
        )
    )
)

REM Check standalone service module (only needed in build-test mode)
if not exist "%STANDALONE_MODULE%" (
    call :log_warning "Standalone service module does not exist: %STANDALONE_MODULE%"
    call :log_warning "Test server cannot be started in build-test mode"
) else (
    call :log_info "Standalone service module exists: %STANDALONE_MODULE%"
)

REM Report missing modules
if not "%missing_modules%"=="" (
    call :log_warning "The following module directories do not exist: %missing_modules%"
    call :log_warning "Will skip compilation of these modules"
)

call :log_success "Project structure validation completed"
exit /b 0

:clean_environment
call :log_step "4" "Cleaning build and test environment"

call :log_info "Cleaning Maven build cache..."

REM Clean root target directory
if exist "target" (
    rmdir /s /q "target" 2>nul
    call :log_info "Cleaned root target directory"
)

REM Clean each module's target directory
for %%m in (%PROJECT_MODULES%) do (
    if exist "%%m" (
        if exist "%%m\target" (
            rmdir /s /q "%%m\target" 2>nul
            call :log_info "Cleaned module target directory: %%m"
        )
    )
)

REM Clean standalone service module target directory
if exist "%STANDALONE_MODULE%" (
    if exist "%STANDALONE_MODULE%\target" (
        rmdir /s /q "%STANDALONE_MODULE%\target" 2>nul
        call :log_info "Cleaned standalone service module target directory: %STANDALONE_MODULE%"
    )
)

call :log_success "Environment cleanup completed"
exit /b 0

:compile_project
call :log_step "5" "Compiling project"

call :log_info "Starting compilation of entire project..."
call :log_info "Compile command: mvn clean compile -T 1C"

REM Execute Maven compilation with parallel compilation for speed
mvn clean compile -T 1C -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Dmaven.compiler.encoding=UTF-8 -Dproject.build.sourceEncoding=UTF-8 >> "%MAIN_LOG%" 2>&1
set "compile_result=!errorlevel!"

if !compile_result! equ 0 (
    call :log_success "Project compilation successful"
    exit /b 0
) else (
    call :log_error "Project compilation failed"
    call :log_error "For detailed error information, please check log file: %MAIN_LOG%"
    exit /b 1
)

:install_project
call :log_step "6" "Installing project to local Maven repository"

call :log_info "Starting installation of project to local Maven repository..."
call :log_info "Install command: mvn install -DskipTests -T 1C"

REM Execute Maven install, skip tests for speed
mvn install -DskipTests -T 1C -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Dmaven.compiler.encoding=UTF-8 -Dproject.build.sourceEncoding=UTF-8 >> "%MAIN_LOG%" 2>&1
set "install_result=!errorlevel!"

if !install_result! equ 0 (
    call :log_success "Project installation successful"
    exit /b 0
) else (
    call :log_error "Project installation failed"
    call :log_error "For detailed error information, please check log file: %MAIN_LOG%"
    exit /b 1
)

:show_help
echo ========================================================
echo     %SCRIPT_NAME%
echo     Version: %SCRIPT_VERSION%
echo ========================================================
echo.
echo Usage: %~nx0 [mode] [options]
echo.
echo Execution modes:
echo   build                 Execute build only (compile+install), no tests
echo   build-test            Execute complete process (build+test) [default]
echo.
echo Java environment options:
echo   --java-home PATH      Specify Java JDK path
echo.
echo Other options:
echo   --help                Show this help information
echo.
echo Usage examples:
echo   %~nx0                              # Complete build and test process
echo   %~nx0 build                        # Build project only
echo   %~nx0 build-test                   # Build and test project
echo   %~nx0 --java-home C:\Java\jdk8     # Use specified Java path
echo.
goto :eof

REM ==================== Main Program ====================

:main
REM Parse command line arguments
set "custom_java_home="
set "execution_mode=%MODE_BUILD_TEST%"

:parse_main_args
if "%~1"=="" goto :main_args_done
if "%~1"=="build" (
    set "execution_mode=%MODE_BUILD%"
    shift
    goto :parse_main_args
)
if "%~1"=="build-test" (
    set "execution_mode=%MODE_BUILD_TEST%"
    shift
    goto :parse_main_args
)
if "%~1"=="--java-home" (
    set "custom_java_home=%~2"
    shift
    shift
    goto :parse_main_args
)
if "%~1"=="--help" (
    call :show_help
    exit /b 0
)
call :log_error "Unknown option: %~1"
echo.
call :show_help
exit /b 1

:main_args_done
REM Show script header
call :show_header

REM Initialize logging
call :init_logging

call :log_info "Execution mode: %execution_mode%"

REM Execute main process
set "execution_failed=false"

REM Steps 1-4: Environment check and preparation
call :setup_java_environment "%custom_java_home%"
if errorlevel 1 set "execution_failed=true"

if "%execution_failed%"=="false" (
    call :check_maven_environment
    if errorlevel 1 set "execution_failed=true"
)

if "%execution_failed%"=="false" (
    call :validate_project_structure
    if errorlevel 1 set "execution_failed=true"
)

if "%execution_failed%"=="false" (
    call :clean_environment
    if errorlevel 1 set "execution_failed=true"
)

REM Steps 5-6: Build process
if "%execution_failed%"=="false" (
    call :compile_project
    if errorlevel 1 set "execution_failed=true"
)

if "%execution_failed%"=="false" (
    call :install_project
    if errorlevel 1 set "execution_failed=true"
)

REM Return result
if "%execution_failed%"=="true" (
    call :log_error "Execution failed"
    exit /b 1
) else (
    if "%execution_mode%"=="%MODE_BUILD%" (
        call :log_success "Build completed successfully"
    ) else (
        call :log_success "Build and test completed successfully"
    )
    exit /b 0
)

REM Execute main function
call :main %*
